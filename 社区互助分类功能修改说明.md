# 社区互助分类功能修改说明

## 修改概述

根据需求，在 systemInfo.js 中添加了 `communityHelpServiceCategories` 配置，并修改了相关页面的分类选择逻辑，实现了以下功能：

1. **分类筛选下拉**：显示需求分类和服务分类的所有数据（去重后）
2. **发布需求时**：分类选择直接使用 `communityHelpRequireCategories`
3. **发布服务时**：分类选择直接使用 `communityHelpServiceCategories`

## 具体修改内容

### 1. systemInfo.js 修改

**新增方法：**
- `getCommunityHelpServiceCategories()` - 获取社区互助服务分类列表
- `getFormattedHelpServiceCategories()` - 获取格式化的互助服务分类数据
- `getHelpServiceCategoryNameMap()` - 获取互助服务分类名称映射

**重命名方法：**
- `getFormattedHelpCategories()` → `getFormattedHelpRequireCategories()` (新增，保留旧方法兼容)
- `getHelpCategoryNameMap()` → `getHelpRequireCategoryNameMap()` (新增，保留旧方法兼容)

### 2. 社区互助列表页面 (community-help/index.js)

**修改 `loadCategories()` 方法：**
- 同时获取需求分类和服务分类数据
- 合并两个分类集合并去重（基于 dictValue）用于筛选显示
- 在页面数据中存储 `requireCategories` 和 `serviceCategories`

**新增 `getPublishCategories()` 方法：**
- 根据发布类型（需求/服务）返回对应的分类数据

**修改 index.wxml：**
- 发布分类选择根据 `selectedPublishType` 动态显示对应分类
- 发布需求时显示 `requireCategories`
- 提供服务时显示 `serviceCategories`

### 3. 社区互助发布页面 (community-help-publish/index.js)

**修改 `loadCategories()` 方法：**
- 同时获取需求分类和服务分类数据
- 存储两种分类数据到页面数据中

**新增 `getCurrentCategories()` 方法：**
- 根据当前发布类型返回对应的分类数据

**修改 `updateCategoriesByPublishType()` 方法：**
- 根据当前发布类型动态设置对应的分类数据
- 发布需求时使用需求分类，提供服务时使用服务分类

**移除 `categories` 字段：**
- 不再使用统一的 `categories` 字段
- 直接从 `requireCategories` 或 `serviceCategories` 获取数据

**调用时机：**
- 页面初始化时（新增模式）
- 编辑模式加载详情后
- 分类数据加载完成后

## 数据流程

1. **筛选功能**：
   ```
   requireCategories + serviceCategories → 去重 → 筛选下拉选项
   ```

2. **发布需求**：
   ```
   选择"发布需求" → selectedPublishType='1' → 界面显示 requireCategories
   ```

3. **提供服务**：
   ```
   选择"提供服务" → selectedPublishType='2' → 界面显示 serviceCategories
   ```

4. **发布页面分类选择**：
   ```
   getCurrentCategories() → 根据 formData.publishType 返回对应分类数据
   ```

## 核心改进

1. **简化数据结构**：移除了不必要的 `categories` 字段和兜底逻辑
2. **直接数据映射**：发布类型直接对应相应的分类数据
3. **清晰的职责分离**：需求分类和服务分类完全独立
4. **动态界面渲染**：根据发布类型动态显示对应分类列表

## 注意事项

1. 需要在后端配置 `communityHelpServiceCategories` 字典数据
2. 两个分类集合的数据格式必须完全一致（dictValue, dictLabel）
3. 去重逻辑仅用于筛选功能，发布功能直接使用对应的分类数据
4. 发布页面不再使用统一的 `categories` 字段，直接从对应分类数据获取
